# Data koleksi obyek (O)
docs = {
    1: 'Sate ayam madura adalah sate dengan bumbu kacang khas madura',
    2: 'Rendang padang merupakan makanan khas sumatera barat dengan bumbu rempah',
    3: 'G<PERSON>g jogja memiliki rasa manis yang khas dari nangka muda dan gula aren',
    4: 'Sate padang menggunakan bumbu kuah kuning kental yang kaya rempah',
}

# 1. Representasi Obyek
print("=== Representasi Obyek ===")
for i, text in docs.items():
    print(f"Obj {i} -> D{i}: ", text.lower().split())

# 2. Inverted Index
index = defaultdict(set)
for doc_id, text in docs.items():
    for word in text.lower().split():
        index[word].add(doc_id)

print("\n=== Dictionary | Inverted List ===")
for term in sorted(index.keys()):
    print(f"{term:15} | {sorted(list(index[term]))}")

# 3. <PERSON><PERSON>i <PERSON>trieval Bo<PERSON>
def show_boolean_results(term1, term2):
    set1 = index.get(term1, set())
    set2 = index.get(term2, set())

    and_docs = set1 & set2
    or_docs = set1 | set2
    not_docs = set1 - set2

    print(f"\n=== Hasil Retrieval untuk Query: '{term1} {term2}' ===")
    print(f"{term1} (S1) -> {sorted(list(set1))}")
    print(f"{term2} (S2) -> {sorted(list(set2))}")

    print("\nRetrieved object (R) dengan logika AND:")
    if and_docs:
        for d in sorted(list(and_docs)):
            print(f"Obj {d}: {docs[d]}")
    else:
        print("(Tidak ada hasil)")

    print("\nRetrieved object (R) dengan logika OR:")
    if or_docs:
        for d in sorted(list(or_docs)):
            print(f"Obj {d}: {docs[d]}")
    else:
        print("(Tidak ada hasil)")


    print("\nRetrieved object (R) dengan logika NOT (S1 NOT S2):")
    if not_docs:
        for d in sorted(list(not_docs)):
            print(f"Obj {d}: {docs[d]}")
    else:
        print("(Tidak ada hasil)")


# Menjalankan Retrieval untuk setiap query
show_boolean_results("sate", "bumbu")
show_boolean_results("padang", "rempah")
