# Koleksi dokumen
docs = [
    "mahasiswa fasilkom universitas jember adalah mahasiswa hebat",
    "universitas jember memiliki 30.000 mahasiswa",
    "saat ini fasilkom universitas jember memiliki tiga prodi",
    "prodi pertama fasilkom universitas jember saat ini adalah prodi sistem informasi"
]

# Token<PERSON><PERSON> sederhana
tokenized_docs = [doc.split() for doc in docs]

# Membuat dictionary (himpunan kata unik)
terms = sorted(set([word for doc in tokenized_docs for word in doc]))

# Membuat term-by-document matrix (binary weighting)
matrix = []
for doc in tokenized_docs:
    row = [1 if term in doc else 0 for term in terms]
    matrix.append(row)

# Membuat inverted index
inverted_index = {}
for i, term in enumerate(terms):
    postings = [j+1 for j, doc in enumerate(tokenized_docs) if term in doc]
    inverted_index[term] = postings

print("Dictionary:", terms)
print("Matrix (term-by-doc):", matrix)
print("Inverted Index:", inverted_index)
